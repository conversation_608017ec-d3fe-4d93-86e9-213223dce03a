'use client'

import { useTranslations } from 'next-intl'
import { useVolcAnalytics } from '@ninebot/core'
import { TRACK_EVENT } from '@ninebot/core/src/constants/global'
import {
  checkoutPaymentMethodsSelector,
  checkoutSelectedPaymentMethodSelector,
  setCheckoutPlaceOrderPaymentMethodCode,
} from '@ninebot/core/src/store'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
import { Image } from 'antd-mobile'
import { CheckList } from 'antd-mobile'

import { CheckOutlined } from '@/components/icons'

// 定义支付方式类型
type AvailablePaymentMethod = {
  __typename?: 'AvailablePaymentMethod'
  code: string
  title: string
  logo?: string | null
}

export default function PaymentSection() {
  const dispatch = useAppDispatch()
  const { reportEvent } = useVolcAnalytics()
  const getI18nString = useTranslations('Common')
  const paymentMethods = useAppSelector(checkoutPaymentMethodsSelector)
  const selectedPaymentMethod = useAppSelector(checkoutSelectedPaymentMethodSelector)

  /**
   * 设置选中的 payment method
   */
  const handleSelectPaymentMethod = (code: string) => {
    reportEvent(TRACK_EVENT.shop_payment_method_click, {
      payment_method_id: code,
    })
    dispatch(setCheckoutPlaceOrderPaymentMethodCode(code))
  }

  return (
    <CheckList
      className="payment-checklist"
      value={selectedPaymentMethod?.code ? [selectedPaymentMethod.code] : []}
      onChange={(val) => handleSelectPaymentMethod(val[0] as string)}
      activeIcon={<CheckOutlined />}
      defaultValue={selectedPaymentMethod?.code ? [selectedPaymentMethod.code] : []}>
      <div className="bg-[#F8F8F9]">
        <div className="overflow-hidden rounded-base-12 bg-white px-base-12 py-base-16">
          <h2 className="mb-base-16 font-miSansDemiBold450 text-lg">
            {getI18nString('recommended_payment_method')}
          </h2>
          <div className="flex flex-col gap-[4px]">
            {paymentMethods
              .filter((payment): payment is AvailablePaymentMethod => payment !== null)
              .map((payment) => (
                <CheckList.Item key={payment.code} value={payment.code}>
                  <div className="flex w-full items-center gap-2">
                    {payment.logo && (
                      <Image
                        src={payment.logo}
                        alt={payment.title || '支付方式图标'}
                        width={24}
                        height={24}
                      />
                    )}
                    <span className="font-miSansMedium380 text-[14px] leading-[22.4px]">
                      {payment.title}
                    </span>
                  </div>
                </CheckList.Item>
              ))}
          </div>
        </div>
      </div>
    </CheckList>
  )
}
