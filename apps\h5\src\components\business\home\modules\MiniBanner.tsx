import { memo } from 'react'
import { Image } from 'antd-mobile'
import clsx from 'clsx'

import { Arrow } from '@/components/icons'
import { Link } from '@/i18n/navigation'

interface BannerItem {
  id: string
  image: string
  title: string
  price?: string
  viewMoreText?: string
  showArrow?: boolean
  link: string
}

const BannerCard = memo(
  ({ image, title, price, viewMoreText = '了解更多', showArrow, link }: BannerItem) => (
    <Link
      href={link}
      className="block h-full overflow-hidden rounded-3xl bg-[#F8F8F8] transition-shadow duration-300 hover:shadow-md">
      <div className="relative h-full min-h-[200px]">
        <div className="absolute left-4 top-4 z-10 md:left-6 md:top-6">
          <h3 className="font-miSansDemiBold450 text-3xl">{title}</h3>
          <div className="mt-1 flex items-center">
            {price ? (
              <span className="text-base md:text-lg">¥{price}</span>
            ) : (
              <div className="flex items-center hover:text-primary">
                <span className="text-sm md:text-base">{viewMoreText}</span>
                {showArrow && <Arrow />}
              </div>
            )}
          </div>
        </div>
        <div className="relative h-full w-full">
          <Image
            src={image}
            alt={title || 'Mini Banner图片'}
            sizes="(max-width: 1000px) 100vw, 1000px"
            className="object-contain"
          />
        </div>
      </div>
    </Link>
  ),
)

BannerCard.displayName = 'BannerCard'

const MiniBanner = ({ items }: { items: BannerItem[] }) => {
  if (!items?.length) return null

  // 根据items数量决定布局
  const getGridLayout = () => {
    switch (items.length) {
      case 1:
        return 'grid-cols-1'
      case 2:
        return 'grid-cols-2 gap-4'
      case 3:
        // 修改为一列布局
        return 'grid-cols-1 gap-4'
      case 4:
        return 'grid-cols-2 gap-4'
      default:
        return ''
    }
  }

  // 获取每个banner的样式
  const getBannerStyle = (index: number) => {
    if (items.length === 3) {
      if (index === 0) {
        return 'h-[200px]' // 第一个banner高度
      }
      if (index > 0) {
        return 'h-[160px]' // 下面两个banner的高度
      }
    }
    if (items.length === 4) {
      return 'aspect-[2/1]'
    }
    return ''
  }

  // 获取容器样式
  const getContainerStyle = (index: number) => {
    if (items.length === 3 && index > 0) {
      return 'grid grid-cols-2 gap-4' // 下面两个banner并排显示
    }
    return ''
  }

  return (
    <div className="mx-auto py-6 md:py-8">
      <div className={clsx('grid', getGridLayout(), items.length === 4 && 'grid-rows-2')}>
        {items.slice(0, 4).map((item, index) => (
          <div key={item.id} className={getContainerStyle(index)}>
            {items.length === 3 && index === 1 ? (
              // 当是3个banner时,把后两个banner放在同一行
              <>
                <div className={getBannerStyle(1)}>
                  <BannerCard {...items[1]} />
                </div>
                <div className={getBannerStyle(2)}>
                  <BannerCard {...items[2]} />
                </div>
              </>
            ) : items.length === 3 && index === 2 ? null : (
              <div className={getBannerStyle(index)}>
                <BannerCard {...item} />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

export default memo(MiniBanner)
