'use client'

import { But<PERSON> } from 'antd-mobile'
import clsx from 'clsx'

import { Link } from '@/i18n/navigation'

import { Arrow } from '../icons'

import { CustomEmpty } from './customEmpty'
export type TEmptyDataProps = {
  emptyIcon?: React.ReactNode
  title?: string
  message?: string
  btnText?: string
  link?: string
  titleStyle?: string
  messageStyle?: string
  btnStyle?: string
  pathname?: string
  isActionBtnVisible?: boolean
  isArrowBtnVisible?: boolean
}

/**
 * 数据空展示
 */
const EmptyData = (props: TEmptyDataProps) => {
  // TODO: 下面文案翻译
  const {
    emptyIcon,
    title,
    message = '暂无内容',
    btnText = '返回首页',
    link,
    titleStyle = '',
    messageStyle = '',
    btnStyle = '',
    pathname,
    isActionBtnVisible = true,
    isArrowBtnVisible = true,
  } = props

  return (
    <>
      {pathname && (
        <div className="hidden border-b border-gray-2 py-7 xl:block">
          <div className="max-page-content px-4 xl:px-0">
            <div className="flex items-center gap-2 text-sm text-gray-4">
              <Link href="/" className="hover:text-black">
                首页
              </Link>
              <span>/</span>
              <Link href={`/${pathname}`} className="hover:text-black">
                {pathname.split('/')[pathname.split('/').length - 1]}
              </Link>
            </div>
          </div>
        </div>
      )}
      <div className="flex flex-col items-center justify-center">
        {emptyIcon ? emptyIcon : <CustomEmpty />}
        {title && (
          <h2 className={clsx('mt-3 font-miSansDemiBold450 text-2xl', titleStyle)}>{title}</h2>
        )}
        {message && (
          <p className={clsx('font-miSansRegular330 text-base xl:text-lg', messageStyle)}>
            {message}
          </p>
        )}
        {btnText && isActionBtnVisible && (
          <div className="mt-6 flex items-center gap-1.5 xl:mt-7">
            <Button
              className={clsx(
                'xl:text-pc-xl h-[22px] border-none p-0 text-base text-black hover:bg-transparent',
                btnStyle,
              )}>
              <Link href={link ? link : '/'}>{btnText}</Link>
            </Button>
            {isArrowBtnVisible && <Arrow color="#000000" />}
          </div>
        )}
      </div>
    </>
  )
}

export default EmptyData
