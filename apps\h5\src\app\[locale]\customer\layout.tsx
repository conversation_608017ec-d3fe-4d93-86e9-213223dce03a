'use client'

import { ReactNode } from 'react'
import { usePathname } from 'next/navigation'
import { useScrollToTop } from '@ninebot/core'

import { Header } from '@/components'
const Layout = ({ children }: { children: ReactNode }) => {
  const pathName = usePathname()
  const isWhite = pathName !== '/customer/account' && !pathName.startsWith('/customer/returns')

  useScrollToTop({
    trigger: 'manual',
    scrollOnMount: true,
    behavior: 'auto',
  })
  return (
    <div className={`min-h-screen ${isWhite ? 'bg-white' : 'bg-gray-base'}`}>
      <Header isTransparent={false} />
      {children}
    </div>
  )
}

export default Layout
