'use client'

import React, { useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  mergeStyles,
  OrderReturnCarrier,
  resolveCatchMessage,
  useAddAfterSaleTrackMutation,
  useDebounceFn,
  useLoadingContext,
  userOrderReturnCarriersSelector,
  useToastContext,
} from '@ninebot/core'
import { sleep } from '@ninebot/core'
import { useAppSelector } from '@ninebot/core/src/store/hooks'
import { Button, Input } from 'antd-mobile'

import { JumpToCustomerService } from '@/businessComponents'
import { Arrow, CustomNavBar, IconService } from '@/components'
import { useRouter } from '@/i18n/navigation'

import { ChooseTrackCompany } from './components'

const styles = {
  formItem: 'flex flex-row items-center justify-between py-[12px]',
  label: 'w-[88px] text-[#444446]',
  inputContainer: 'flex flex-row items-center justify-between',
  input: {
    height: 40,
    width: 200,
    color: '#0F0F0F',
    '--font-size': '14px',
    '--placeholder-color': '#86868B',
  },
  asterisk: 'text-[#DA291C] mt-[6px]',
}

/**
 * 退款详情页
 */
const AccountOrderReturnTrack = ({ returnId }: { returnId: string }) => {
  const trackCompany = useAppSelector(userOrderReturnCarriersSelector)
  const getI18nString = useTranslations('Common')
  const router = useRouter()
  const loading = useLoadingContext()
  const toast = useToastContext()

  const [trackName, setTrackName] = useState<OrderReturnCarrier>()
  const [otherTrackName, setOtherTrackName] = useState('')
  const [trackNumber, setTrackNumber] = useState('')

  const [popupVisible, setPopupVisible] = useState(false)

  const [addAfterSaleTrack] = useAddAfterSaleTrackMutation()

  const goBack = () => {
    router.back()
  }

  /**
   * 创建物流
   */
  const { run: createAfterSaleTrack } = useDebounceFn(() => {
    if (returnId) {
      loading.show()
      addAfterSaleTrack({
        id: returnId,
        company:
          trackName?.carrier_title === '其它' ? otherTrackName : String(trackName?.carrier_code),
        trackNo: trackNumber,
      })
        .unwrap()
        .then(async (res) => {
          loading.hide()
          await sleep(500)
          if (res?.requisitionTrack) {
            toast.show({
              icon: 'success',
              content: getI18nString('submit_success'),
            })
            await sleep(500)
            goBack()
          } else {
            toast.show({
              icon: 'fail',
              content: getI18nString('submit_failed'),
            })
          }
        })
        .catch(async (error) => {
          loading.hide()
          await sleep(500)
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          })
        })
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  })

  /**
   * 提交物流信息
   */
  const handleSubmit = () => {
    if (trackName === null || !trackName) {
      toast.show({
        content: getI18nString('please_choose_track_company'),
      })
      return
    }
    if (trackName?.carrier_title === '其它' && otherTrackName === '') {
      toast.show({
        content: getI18nString('please_input_track_company'),
      })
      return
    }
    if (trackNumber === '') {
      toast.show({
        content: getI18nString('please_input_track_number'),
      })
      return
    }

    createAfterSaleTrack()
  }

  return (
    <div>
      <CustomNavBar
        title={getI18nString('input_track_info')}
        right={
          <div className="float-end hidden">
            <JumpToCustomerService
              udeskParams={{
                type: 'order',
                targetId: '',
              }}>
              <IconService />
            </JumpToCustomerService>
          </div>
        }
        onBack={goBack}
      />
      <div>
        <div className="mx-[12px]">
          <div className="mt-[8px] rounded-[8px] bg-white px-[16px]">
            <div className={styles.formItem}>
              <div className={styles.label}>{getI18nString('track_company')}</div>
              <div className={styles.inputContainer} onClick={() => setPopupVisible(true)}>
                <Input
                  style={{
                    ...styles.input,
                  }}
                  disabled={false}
                  value={trackName?.carrier_title || ''}
                  placeholder={getI18nString('choose_track_company')}
                  readOnly
                />
                <div className="h-8 w-8">
                  <Arrow color="#86868B" rotate={0} size={20} />
                </div>
                <div className={styles.asterisk}>*</div>
              </div>
            </div>

            {trackName?.carrier_title === '其它' ? (
              <div className={styles.formItem}>
                <div className={styles.label}>{getI18nString('other_track')}</div>
                <div className={styles.inputContainer}>
                  <Input
                    style={styles.input}
                    placeholder={getI18nString('input_track_company')}
                    value={otherTrackName}
                    onChange={setOtherTrackName}
                  />
                  <div className={mergeStyles([styles.asterisk, 'ml-[14px]'])}>*</div>
                </div>
              </div>
            ) : null}

            <div className={styles.formItem}>
              <div className={styles.label}>{getI18nString('tracking_number')}</div>
              <div className={styles.inputContainer}>
                <Input
                  style={styles.input}
                  value={trackNumber}
                  type="number"
                  onChange={setTrackNumber}
                  placeholder={getI18nString('input_track_number')}
                />
                <div className={mergeStyles([styles.asterisk, 'ml-[14px]'])}>*</div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部占位符 */}
        <div className="h-[72px]" />

        {/* 物流公司弹窗 */}
        <ChooseTrackCompany
          trackCompany={trackCompany}
          popupVisible={popupVisible}
          onSelect={setTrackName}
          closePopup={() => {
            setPopupVisible(false)
          }}
        />
      </div>

      {/* 固定底部按钮 */}
      <div className="max-container fixed bottom-0 z-50 flex h-[72px] items-center justify-center bg-white p-[12px] shadow-[0_-2px_8px_rgba(0,0,0,0.06)]">
        <Button className="nb-button w-full" color="primary" onClick={handleSubmit}>
          <div>{getI18nString('submit')}</div>
        </Button>
      </div>
    </div>
  )
}

export default AccountOrderReturnTrack
