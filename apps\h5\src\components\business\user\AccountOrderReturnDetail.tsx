'use client'

import React, { useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  IconPlus,
  mergeStyles,
  NCoinView,
  OrderReturnCarrier,
  OrderReturnTracking,
  Price,
  resolveCatchMessage,
  ROUTE,
  setReturnCarriers,
  sleep,
  useCancelOrderReturnMutation,
  useClipboard,
  useDebounceFn,
  useGetAfterSaleDetailQuery,
  useLazyGetAfterSaleTrackQuery,
  useLoadingContext,
  useToastContext,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { useAppDispatch } from '@ninebot/core/src/store/hooks'
import { Button, Dialog } from 'antd-mobile'

import { CustomButton, CustomNavBar, IconCopy } from '@/components'
import { commonStyles } from '@/constants'

import { LogisticsPopup, OrderDetailSkeleton, ProductItem } from './components'

const styles = {
  ...commonStyles,
  container: 'mb-[12px] py-[16px] px-[12px] bg-white rounded-[8px]',
  address: 'pb-8',
  statusTitle: 'text-[20px] leading-[27px] text-[#000000] font-miSansDemiBold450',
  logisticsButton: {
    width: '100%',
    height: 40,
    borderWidth: 1,
    borderColor: '#E1E1E4',
    marginTop: '16px',
  },
  addressRight: 'flex flex-col',
  addressTitle: 'flex flex-row mb-[16px]',
  addressText: 'mt-[8px] text-[14px] leading-8 text-[#444446] font-miSansRegular330',
  detailTitle: `${commonStyles.font_16_bold} mb-[24px]`,
  detailItem: `${commonStyles.flex_row} mb-8`,
}

/**
 * 退款详情页
 */
const AccountOrderReturnDetail = ({ returnId }: { returnId: string }) => {
  const loading = useLoadingContext()
  const toast = useToastContext()
  const { copyToClipboard } = useClipboard()
  const { openPage } = useNavigate()
  const getI18nString = useTranslations('Common')
  const dispatch = useAppDispatch()

  const [logisticsData, setLogisticsData] = useState<OrderReturnTracking>()
  const [logisticsVisible, setLogisticsVisible] = useState(false)

  const [getAfterSaleTrack] = useLazyGetAfterSaleTrackQuery()
  const [cancelOrderReturn] = useCancelOrderReturnMutation()

  const {
    data: response,
    isLoading,
    isFetching,
    refetch,
  } = useGetAfterSaleDetailQuery({
    filter: {
      entity_id: { eq: returnId },
    },
  })

  const orderData = response?.requisitionList?.items?.[0]

  /**
   * 是否含有N币
   */
  const hasNCoin = useMemo(() => Number(orderData?.total?.refund_ncoin) > 0, [orderData?.total])

  /**
   * 是否纯N币订单
   */
  const isOnlyNCoin = useMemo(
    () => hasNCoin && Number(orderData?.total?.refund_total?.value) <= 0,
    [hasNCoin, orderData?.total],
  )

  /**
   * 获取物流信息
   */
  const fetchLogisticsData = async () => {
    if (orderData?.unique_id && orderData?.can_check_carrier) {
      try {
        const res = await getAfterSaleTrack({
          uniqueId: orderData?.unique_id,
        }).unwrap()

        if (res?.requisitionTrackDetail) {
          setLogisticsData(res?.requisitionTrackDetail)
        }
      } catch (error) {
        console.error('Failed to fetch logistics data:', error)
      }
    }
  }

  /**
   * 打开物流信息弹窗
   */
  const openLogisticsPopup = () => {
    if (logisticsData) {
      setLogisticsVisible(true)
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  }

  /**
   * 取消退款申请
   */
  const { run: cancelReturnOrder } = useDebounceFn(() => {
    if (orderData?.encrypt?.nid) {
      loading.show()
      cancelOrderReturn({
        orderNumber: orderData.encrypt.nid,
        uniqueId: orderData?.unique_id,
      })
        .unwrap()
        .then(async (res) => {
          loading.hide()
          await sleep(500)
          if (res?.cancelOrderRequisition?.status) {
            toast.show({
              icon: 'success',
              content: getI18nString('cancelled'),
            })
            await sleep(500)
            refetch()
          } else {
            toast.show({
              icon: 'fail',
              content: res?.cancelOrderRequisition?.message || getI18nString('fetch_data_error'),
            })
          }
        })
        .catch(async (error) => {
          loading.hide()
          await sleep(500)
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          })
        })
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  })

  useEffect(() => {
    if (!isLoading && isFetching) {
      loading.show()
    } else {
      loading.hide()
    }
  }, [loading, isLoading, isFetching])

  // 自动获取物流数据
  useEffect(() => {
    if (orderData && !isLoading) {
      fetchLogisticsData()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderData, isLoading])

  /**
   * 填写物流信息
   */
  const { run: handleInputTrack } = useDebounceFn(() => {
    dispatch(setReturnCarriers(orderData?.return_carriers as OrderReturnCarrier[]))
    openPage({
      route: ROUTE.accountOrderReturnTrack,
      queryParams: {
        returnId: orderData?.id || '',
      },
    })
  })

  useEffect(() => {
    // 如果没拿到售后单数据，跳到订单列表
    if (!isLoading && !orderData) {
      openPage({
        route: ROUTE.accountOrder,
      })
    }
  }, [isLoading, orderData, openPage])

  /**
   * 撤销退款申请确认弹窗
   */
  const handleCancelRequisition = () => {
    Dialog.confirm({
      bodyClassName: 'custom-dialog-confirm',
      title: getI18nString('confirm_cancel_return_order'),
      cancelText: getI18nString('thinking'),
      confirmText: getI18nString('confirm_cancel'),
      onConfirm: () => {
        cancelReturnOrder()
      },
    })
  }

  return (
    <div>
      <CustomNavBar title={getI18nString('return_detail')} />
      {isLoading || !orderData ? (
        <OrderDetailSkeleton />
      ) : (
        <div className="mx-[12px] pt-[8px]">
          {/* 退款状态 */}
          <div className={mergeStyles([styles.container, 'px-[16px] py-[20px]'])}>
            <div
              className={
                orderData?.requisition_comments?.comment
                  ? styles.statusTitle
                  : mergeStyles([styles.statusTitle, 'mb-8'])
              }>
              {orderData?.requisition_comments?.title}
            </div>
            {orderData?.requisition_comments?.pickup_code ? (
              <div className={mergeStyles(['mb-8 mt-[8px]', styles.flex_row, 'gap-[16px]'])}>
                <div className="font-miSansRegular330 text-[12px] leading-[17px] text-[#86868B]">
                  <span className={mergeStyles([styles.font_red, 'font-miSansDemiBold450'])}>
                    {orderData?.requisition_comments?.pickup_date}{' '}
                    {orderData?.requisition_comments?.pickup_time}{' '}
                  </span>
                  <span>{orderData?.requisition_comments?.comment}</span>
                </div>
                <div
                  className={
                    'flex h-[68px] flex-col items-center justify-between rounded-[8px] bg-white px-[14px]'
                  }
                  style={{ boxShadow: '0px 0px 10px 0px rgba(0, 0, 0, 0.15)' }}>
                  <div className="mt-[10px] font-miSansBold630 text-[22px] leading-[31px] text-[#DA291C]">
                    {orderData?.requisition_comments?.pickup_code}
                  </div>
                  <div className="mb-[4px] w-full bg-[#FAFAFA] text-center font-miSansRegular330 text-[12px] text-[#A3A3A3]">
                    {getI18nString('return_pickup_code')}
                  </div>
                </div>
              </div>
            ) : orderData?.requisition_comments?.comment ? (
              <div className="mb-0 mt-[8px] rounded-[4px] bg-[#F8F8F9] px-[16px] py-[12px]">
                <div className="font-miSansRegular330 text-[12px] leading-[17px] text-[#86868B]">
                  {orderData?.requisition_comments?.comment}
                </div>
              </div>
            ) : null}
            {orderData?.can_edit_carrier ? (
              <CustomButton
                customStyle={styles.logisticsButton}
                fill="outline"
                onClick={handleInputTrack}>
                <div className={styles.font_14}>{getI18nString('input_tracking_number')}</div>
              </CustomButton>
            ) : null}

            {orderData?.can_check_carrier && logisticsData ? (
              <CustomButton
                customStyle={styles.logisticsButton}
                fill="outline"
                onClick={openLogisticsPopup}>
                <div className={styles.font_14}>{getI18nString('view_tracking')}</div>
              </CustomButton>
            ) : null}
          </div>

          {/* 寄件地址 */}
          {orderData?.return_shipping_address ? (
            <div className={mergeStyles([styles.container, styles.address])}>
              <div className={styles.addressRight}>
                <div className={styles.addressTitle}>
                  <div className={mergeStyles([styles.font_16, 'mr-[8px]'])}>
                    {getI18nString('my_sending_address')}
                  </div>
                </div>
                <div>
                  <div className={styles.flex_start}>
                    <div className={styles.font_16_bold}>
                      {orderData?.return_shipping_address?.contact}
                    </div>
                  </div>
                  <div className={styles.addressText}>
                    {orderData?.return_shipping_address?.address}
                  </div>
                </div>
              </div>
            </div>
          ) : null}
          {/* 退货地址 */}
          {orderData?.return_address ? (
            <div className={mergeStyles([styles.container, styles.address])}>
              <div className={styles.addressRight}>
                <div className={styles.addressTitle}>
                  <div className={mergeStyles([styles.font_16, 'mr-[8px]'])}>
                    {getI18nString('return_address')}
                  </div>
                  <CustomButton
                    customStyle={{
                      width: 16,
                      height: 18,
                      padding: 0,
                    }}
                    onClick={() => {
                      copyToClipboard(orderData?.return_address?.address || '')
                    }}>
                    <IconCopy />
                  </CustomButton>
                </div>
                <div>
                  <div className={styles.flex_start}>
                    <div className={styles.font_16_bold}>{orderData?.return_address?.contact}</div>
                  </div>
                  <div className={styles.addressText}>{orderData?.return_address?.address}</div>
                </div>
              </div>
            </div>
          ) : null}
          {/* 商品清单 */}
          <div className={styles.container}>
            <div className={mergeStyles([styles.font_16_bold, 'mb-[4px]'])}>
              {getI18nString('product_list')}
            </div>
            {Number(orderData?.items?.length) > 0 &&
              orderData?.items?.map((item) => {
                return (
                  <div key={item?.id} className="mt-8">
                    <ProductItem productInfo={item} isReturn={true} showQty={true} />
                  </div>
                )
              })}
          </div>
          {/* 退款金额 */}
          <div className={mergeStyles([styles.container, ''])}>
            <div className={styles.detailTitle}>{getI18nString('return_price')}</div>
            <div className={styles.detailItem}>
              <div className={styles.font_14_light}>{getI18nString('product_price')}</div>
              {isOnlyNCoin ? (
                <NCoinView
                  number={Number(orderData?.total?.refund_ncoin)}
                  iconStyle={{ size: 14 }}
                  textStyle={mergeStyles([styles.font_14, 'leading-[18px]'])}
                />
              ) : hasNCoin ? (
                <>
                  <Price
                    price={orderData?.total?.subtotal}
                    currencyStyle={styles.font_14}
                    textStyle={styles.font_14}
                    fractionStyle={styles.font_14}
                  />
                  <div className="mx-[4px]">
                    <IconPlus />
                  </div>
                  <NCoinView
                    number={Number(orderData?.total?.refund_ncoin)}
                    iconStyle={{ size: 14 }}
                    textStyle={mergeStyles([styles.font_14, 'leading-[18px]'])}
                  />
                </>
              ) : (
                <Price
                  price={orderData?.total?.subtotal}
                  currencyStyle={styles.font_14}
                  textStyle={styles.font_14}
                  fractionStyle={styles.font_14}
                />
              )}
            </div>
            {Number(orderData?.total?.refund_shipping?.value) > 0 ? (
              <div className={styles.detailItem}>
                <div className={styles.font_14_light}>{getI18nString('shipping_price')}</div>
                <Price
                  price={orderData?.total?.refund_shipping}
                  currencyStyle={styles.font_14}
                  textStyle={styles.font_14}
                  fractionStyle={styles.font_14}
                />
              </div>
            ) : null}

            <div className={styles.detailItem}>
              <div className={styles.font_14_light}>{getI18nString('total_total')}</div>
              {isOnlyNCoin ? (
                <NCoinView
                  number={Number(orderData?.total?.refund_ncoin)}
                  iconStyle={{ size: 14 }}
                  textStyle={mergeStyles([styles.font_14, 'leading-[18px]'])}
                />
              ) : hasNCoin ? (
                <>
                  <Price
                    price={orderData?.total?.refund_total}
                    currencyStyle={styles.font_14}
                    textStyle={styles.font_14}
                    fractionStyle={styles.font_14}
                  />
                  <div className="mx-[4px]">
                    <IconPlus />
                  </div>
                  <NCoinView
                    number={Number(orderData?.total?.refund_ncoin)}
                    iconStyle={{ size: 14 }}
                    textStyle={mergeStyles([styles.font_14, 'leading-[18px]'])}
                  />
                </>
              ) : (
                <Price
                  price={orderData?.total?.refund_total}
                  currencyStyle={styles.font_14}
                  textStyle={styles.font_14}
                  fractionStyle={styles.font_14}
                />
              )}
            </div>
          </div>
          {/* 售后信息 */}
          <div className={styles.container}>
            <div className={styles.detailTitle}>{getI18nString('return_info')}</div>
            <div className={styles.detailItem}>
              <div className={styles.font_14_light}>{getI18nString('return_type')}</div>
              <div className={styles.font_14}>{orderData?.requisition_type_label}</div>
            </div>
            <div className={styles.detailItem}>
              <div className={styles.font_14_light}>{getI18nString('after_sale_reason')}</div>
              <div className={styles.font_14}>{orderData?.reason}</div>
            </div>
          </div>
          {/* 查询物流弹窗 */}
          <LogisticsPopup
            popupVisible={logisticsVisible}
            closePopup={() => {
              setLogisticsVisible(false)
            }}
            data={logisticsData}
            showAddress
          />
        </div>
      )}

      {/* 底部操作 */}
      {orderData?.can_cancel ? (
        <>
          {/* 底部占位符 */}
          <div className="h-[72px]" />

          {/* 固定底部按钮 */}
          <div className="max-container fixed bottom-0 z-50 flex h-[72px] items-center justify-center bg-white p-[12px] shadow-[0_-2px_8px_rgba(0,0,0,0.06)]">
            <Button
              className="nb-button !h-[44px] w-full"
              color="primary"
              onClick={handleCancelRequisition}>
              <div className="font-miSansDemiBold450 text-[16px] leading-none text-[#FFFFFF]">
                {getI18nString('cancel_return_request')}
              </div>
            </Button>
          </div>
        </>
      ) : null}
    </div>
  )
}

export default AccountOrderReturnDetail
